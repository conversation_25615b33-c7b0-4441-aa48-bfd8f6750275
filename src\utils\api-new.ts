import httpNew from "./http-new";

/**
 * 新的API接口封装
 * 使用新的后端服务地址：http://172.20.8.143:8080/jeecg-boot
 * 包含新功能相关的API请求方法
 */

// 定义通用的API响应类型
interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
  result: T; // 实际API返回的数据属性名
  success?: boolean;
  message?: string;
}

// 定义通用的API请求参数类型
interface BaseParams {
  [key: string]: any;
}

// 定义菜单项的类型
interface MenuMeta {
  hideMenu?: boolean;
  keepAlive?: boolean;
  internalOrExternal?: boolean;
  hideTab?: boolean;
  icon?: string;
  componentName?: string;
  title: string;
}

interface MenuItem {
  redirect?: string | null;
  path: string;
  component: string;
  route: string;
  children?: MenuItem[];
  hidden?: boolean;
  meta: MenuMeta;
  name: string;
  id: string;
  orderedSupplier?: any;
  defaultSupplier?: any;
  arraySupplier?: any;
}

// 定义用户权限响应类型
interface UserPermissionResponse {
  menu: MenuItem[];
  auth: string[];
  allAuth: string[];
}

/**
 * 新功能相关API
 * 可以根据实际需求添加具体的API方法
 */
export const newApi = {
  /**
   * 新登录接口 - 用于自由交易模块
   * @param {object} params - 登录参数
   * @param {string} params.username - 用户名
   * @param {string} params.password - 密码
   * @param {string} params.captcha - 验证码
   * @param {string} params.checkKey - 验证码标识
   * @param {number} params.loginSource - 登录来源
   * @returns {Promise<ApiResponse>} 登录结果
   */
  login(params: {
    username: string;
    password: string;
    captcha: string;
    checkKey: string;
    loginSource: number;
  }): Promise<ApiResponse> {
    // 时间戳参数由http-new.ts的请求拦截器统一添加
    return httpNew.post("/sys/login", params).then((res) => res.data);
  },

  /**
   * 自由交易模块登录（带验证码）
   * @param {object} data - 登录参数
   * @param {string} data.username - 用户名
   * @param {string} data.password - 密码
   * @param {string} data.captcha - 验证码
   * @param {string} data.checkKey - 验证码标识
   * @param {number} data.loginSource - 登录来源
   * @returns {Promise} 登录结果
   */
  freedomLogin: (data: {
    username: string;
    password: string;
    captcha: string;
    checkKey: string;
    loginSource: number;
  }) => {
    // 时间戳参数由http-new.ts的请求拦截器统一添加
    return httpNew.post("/sys/login", data).then((res) => res.data);
  },

  /**
   * 获取图形验证码
   * @returns {object} 验证码URL和标识
   */
  /**
   * 获取验证码图片URL
   * @returns {object} 包含验证码URL的对象
   */
  getCaptcha(checkKey: string): Promise<ApiResponse> {
    // 时间戳参数由http-new.ts的请求拦截器统一添加
    return httpNew.get(`/sys/randomImage/${checkKey}`).then((res) => res.data);
  },

  /**
   * 获取新登录用户信息
   * @param {object} params - 请求参数
   * @returns {Promise<ApiResponse>} 用户信息
   */
  getUserInfo(params: BaseParams): Promise<ApiResponse> {
    return httpNew.get("/sys/user/info", { params }).then((res) => res.data);
  },

  /**
   * 示例：获取数据列表
   * @param {object} params - 请求参数
   * @returns {Promise<ApiResponse>} 数据列表
   */
  getDataList(params: BaseParams): Promise<ApiResponse> {
    return httpNew.post("/api/data/list", params).then((res) => res.data);
  },

  /**
   * 示例：创建数据
   * @param {object} params - 请求参数
   * @returns {Promise<ApiResponse>} 创建结果
   */
  createData(params: BaseParams): Promise<ApiResponse> {
    return httpNew.post("/api/data/create", params).then((res) => res.data);
  },

  /**
   * 示例：更新数据
   * @param {object} params - 请求参数
   * @returns {Promise<ApiResponse>} 更新结果
   */
  updateData(params: BaseParams): Promise<ApiResponse> {
    return httpNew.put("/api/data/update", params).then((res) => res.data);
  },

  /**
   * 示例：删除数据
   * @param {object} params - 请求参数
   * @returns {Promise<ApiResponse>} 删除结果
   */
  deleteData(params: BaseParams): Promise<ApiResponse> {
    return httpNew
      .delete("/api/data/delete", { params })
      .then((res) => res.data);
  },

  /**
   * 获取自由交易区分页列表
   * @param {object} params - 请求参数
   * @param {number} params.pageNo - 页码
   * @param {number} params.pageSize - 每页数量
   * @param {object} params.queryHgySupplyDemandDto - 查询参数
   * @returns {Promise<ApiResponse>} 自由交易列表数据
   */
  getTradeZonePageList(params: {
    pageNo: number;
    pageSize: number;
    queryHgySupplyDemandDto: {
      infoTitle: string;
    };
  }): Promise<ApiResponse> {
    return httpNew
      .get("/hgy/supplyDemand/hgySupplyDemand/queryTradeZonePageList", {
        params,
      })
      .then((res) => res.data);
  },

  /**
   * 获取供求信息详情
   * @param {string} id - 供求信息ID
   * @returns {Promise<ApiResponse>} 供求信息详情数据
   */
  getSupplyDemandDetail(id: string): Promise<ApiResponse> {
    return httpNew
      // .get("/hgy/supplyDemand/hgySupplyDemand/getByIdAndIsCollect", { params: { id } })
      .get("/hgy/supplyDemand/hgySupplyDemand/queryById", { params: { id } })
      .then((res) => res.data);
  },

  /**
   * 获取资产处置详情
   * @param {string} id - 资产处置ID
   * @returns {Promise<ApiResponse>} 资产处置详情数据
   */
  getAssetEntrustDetail(id: string): Promise<ApiResponse> {
    return httpNew
      .get("/hgy/entrustService/hgyAssetEntrust/getByIdAndIsCollect", { params: { id } })
      .then((res) => res.data);
  },

  /**
   * 增加围观数量
   * @param {string} id - 资产ID
   * @param {number} viewNum - 当前围观数量
   * @returns {Promise<ApiResponse>} 增加围观数量结果
   */
  addViewCount(id: string, viewNum: number): Promise<ApiResponse> {
    return httpNew
      .get("/hgy/supplyDemand/hgySupplyDemand/viewNumAdd", {
        params: { id, viewNum },
      })
      .then((res) => res.data);
  },

  /**
   * 收藏/取消收藏
   * @param {string} itemId - 商品ID
   * @param {string} collectUserId - 收藏用户ID
   * @returns {Promise<ApiResponse>} 操作结果
   */
  collectUnCollect(
    itemId: string,
    collectUserId: string,
  ): Promise<ApiResponse> {
    return httpNew
      .post("/hgy/collect/hgyCollectLog/collectUnCollect", {
        itemId,
        collectUserId,
      })
      .then((res) => res.data);
  },

  /**
   * 发送留言
   * @param {object} params - 留言参数
   * @param {string} params.sendUserId - 发送用户ID
   * @param {string} params.receUserId - 接收用户ID
   * @param {string} params.enterustOrderId - 委托订单ID
   * @param {string} params.message - 留言内容
   * @param {any[]} params.attachmentList - 附件列表（可选）
   * @param {number} params.pid - 父级留言ID，发表留言给资产时传0，回复留言时传顶级留言的id
   * @param {string} params.repliedToId - 被回复的留言ID（可选），回复时传递被回复的这条留言的id
   * @returns {Promise<ApiResponse>} 发送结果
   */
  sendMessage(params: {
    sendUserId: string;
    receUserId: string;
    enterustOrderId: string;
    message: string;
    attachmentList?: any[];
    pid: number;
    repliedToId?: string;
  }): Promise<ApiResponse> {
    return httpNew
      .post("/receive/hgyReceive/sendMessageToUser", params)
      .then((res) => res.data);
  },

  /**
   * 获取留言列表
   * @param {object} params - 查询参数
   * @param {string} params.entrustOrderId - 委托订单ID
   * @param {number} params.pid - 父级留言ID
   * @param {number} params.pageNo - 页码
   * @param {number} params.pageSize - 每页数量，默认50
   * @returns {Promise<ApiResponse>} 留言列表
   */
  getMessageList(params: {
    entrustOrderId: string;
    pid: number;
    pageNo: number;
    pageSize?: number;
  }): Promise<ApiResponse> {
    const requestParams = {
      ...params,
      pageSize: params.pageSize || 50, // 默认50
    };
    return httpNew
      .post("/receive/hgyReceive/queryAllByEntrustOrderId", requestParams)
      .then((res) => res.data);
  },

  /**
   * 获取收到的消息列表
   * @param {object} params - 查询参数
   * @returns {Promise<ApiResponse>} 收到的消息列表
   */
  getReceivedMessages(params: {
    pageNo: number;
    pageSize: number;
    receiveUserId?: string;
    keywords?: string;
  }): Promise<ApiResponse> {
    return httpNew.post('/receive/hgyReceive/queryAllReceive', params).then((res) => res.data)
  },

  /**
   * 获取发送的消息列表
   * @param {object} params - 查询参数
   * @returns {Promise<ApiResponse>} 发送的消息列表
   */
  getSentMessages(params: {
    pageNo: number;
    pageSize: number;
    sendUserId?: string;
    keywords?: string;
  }): Promise<ApiResponse> {
    return httpNew.post('/receive/hgyReceive/queryAllSend', params).then((res) => res.data)
  },

  /**
   * 删除消息
   * @param {string} id - 消息ID
   * @returns {Promise<ApiResponse>} 删除结果
   */
  deleteMessage(id: string): Promise<ApiResponse> {
    return httpNew.delete(`/receive/hgyReceive/deleteReceiveById?id=${id}`).then((res) => res.data)
  },

  /**
   * 获取我的消息列表
   * @param {object} params - 查询参数
   * @returns {Promise<ApiResponse>} 我的消息列表
   */
  getMyMessages(params: {
    pageNo: number;
    pageSize: number;
    title?: string;
  }): Promise<ApiResponse> {
    return httpNew.get('/sys/sysAnnouncementSend/getMyAnnouncementSend', {
      params: {
        ...params,
        column: 'createTime'
      }
    }).then((res) => res.data)
  },

  /**
   * 上传图片
   * @param {FormData} formData - 包含图片文件的FormData
   * @returns {Promise<ApiResponse>} 上传结果
   */
  uploadImage(formData: FormData): Promise<ApiResponse> {
    return httpNew
      .post("/sys/common/upload", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      })
      .then((res) => res.data);
  },

  // 可以根据实际需求继续添加更多API方法
};

// 物资类型树节点类型定义
export interface MaterialTypeNode {
  id: string;
  parentId: string;
  name: string;
  code: string;
  level: number;
  leaf: number;
  sort: number;
  status: number;
  delFlag: number;
  children?: MaterialTypeNode[];
}

// 供应需求信息类型定义
export interface SupplyDemandInfo {
  id?: string;
  entrustOrderId?: string;
  tenantId?: number;
  userId?: string;
  type?: string; // 供求方式 4供应, 5求购
  infoTitle?: string; // 信息标题
  highlights?: string; // 供应亮点
  materialType?: string; // 物资类型
  province?: string; // 省份
  city?: string; // 城市
  district?: string; // 区县
  address?: string; // 详细地址
  materialDesc?: string; // 物资详细描述
  depreciationDegree?: number; // 折旧程度(09-九成新 08-八成新...)
  storageMethod?: number; // 存放方式
  quantity?: number; // 物资数量
  unit?: string; // 物资单位
  price?: number; // 物资价格
  brand?: string; // 物资品牌
  model?: string; // 物资型号
  validDate?: string; // 有效期时间
  relationUser?: string; // 联系人姓名
  relationPhone?: string; // 联系人电话
  status?: number; // 状态(1-草稿 2-提交)
  attachmentList?: any[]; // 附件列表
}

// 保存供应需求参数类型
export interface SaveSupplyDemandParams {
  hgySupplyDemand: SupplyDemandInfo;
  hgyEntrustOrder: {
    relationUser: string;
    relationPhone: string;
    entrustType: number; // 委托类型(1-增值 2-自主 3-供需)
    serviceType: number; // 服务类型(1-竞价委托 2-资产处置 3-采购信息 4-供应 5-求购)
    status: number; // 状态(1-草稿 2-提交)
  };
}

// 供应需求相关API
export const supplyDemandApi = {
  /**
   * 获取物资类型树形数据
   */
  getMaterialTree: (params?: any): Promise<ApiResponse> => {
    return httpNew
      .get("/hgy/material/hgyMaterialType/getMaterialTree", { params })
      .then((res) => res.data);
  },

  /**
   * 保存供应需求信息（新增）
   */
  saveOrderAndSupplyDemand: (
    params: SaveSupplyDemandParams
  ): Promise<ApiResponse> => {
    return httpNew
      .post(
        "/hgy/supplyDemand/hgySupplyDemand/saveOrderAndSupplyDemand",
        params
      )
      .then((res) => res.data);
  },

  /**
   * 更新供应需求信息（修改）
   */
  updateSupplyDemand: (params: SupplyDemandInfo): Promise<ApiResponse> => {
    return httpNew
      .put("/hgy/supplyDemand/hgySupplyDemand/updateSupplyDemand", params)
      .then((res) => res.data);
  },

  /**
   * 根据ID查询供应需求详情
   */
  querySupplyDemandById: (params: { id: string }): Promise<ApiResponse> => {
    return httpNew
      .get("/hgy/supplyDemand/hgySupplyDemand/getByIdAndIsCollect", { params })
      .then((res) => res.data);
  },

  /**
   * 分页查询供应需求列表
   */
  querySupplyDemandList: (params: any): Promise<ApiResponse> => {
    return httpNew
      .get("/hgy/supplyDemand/hgySupplyDemand/list", { params })
      .then((res) => res.data);
  },

  /**
   * 删除供应需求
   */
  deleteSupplyDemand: (params: { id: string }): Promise<ApiResponse> => {
    return httpNew
      .delete("/hgy/supplyDemand/hgySupplyDemand/delete", { params })
      .then((res) => res.data);
  },

  /**
   * 根据ID查询物资类型
   */
  queryMaterialTypeById: (params: { id: string }): Promise<ApiResponse> => {
    return httpNew
      .get("/hgy/material/hgyMaterialType/queryById", { params })
      .then((res) => res.data);
  },
};

// 系统相关API
export const systemApi = {
  /**
   * 根据Token获取用户权限菜单
   * @returns {Promise<ApiResponse<UserPermissionResponse>>} 用户权限数据
   */
  getUserPermissionByToken(): Promise<ApiResponse<UserPermissionResponse>> {
    return httpNew
      .get("/sys/permission/getUserPermissionByToken")
      .then((res) => res.data);
  },

  /**
   * 获取用户数据
   * @returns {Promise<ApiResponse>} 用户数据
   */
  getUserData(): Promise<ApiResponse> {
    return httpNew
      .get("/sys/user/login/setting/getUserData")
      .then((res) => res.data);
  },

  /**
   * 修改用户密码
   * @param {object} params - 修改密码参数
   * @param {string} params.username - 用户名
   * @param {string} params.oldpassword - 旧密码
   * @param {string} params.password - 新密码
   * @param {string} params.confirmpassword - 确认新密码
   * @returns {Promise<ApiResponse>} 修改结果
   */
  updatePassword(params: {
    username: string;
    oldpassword: string;
    password: string;
    confirmpassword: string;
  }): Promise<ApiResponse> {
    return httpNew
      .put("/sys/user/updatePassword", params)
      .then((res) => res.data);
  },

  /**
   * 文件上传
   * @param {FormData} formData - 文件数据
   * @returns {Promise<ApiResponse>} 上传结果
   */
  uploadFile(formData: FormData): Promise<ApiResponse> {
    return httpNew
      .post("/sys/common/upload", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      })
      .then((res) => res.data);
  },

  /**
   * 提交个人认证
   * @param {object} params - 个人认证参数
   * @returns {Promise<ApiResponse>} 提交结果
   */
  submitPersonalAuth(params: any): Promise<ApiResponse> {
    return httpNew
      .post(
        "/hgy/personalCenter/hgyPersonalAuth/addOrUpdatePersonalAuth",
        params
      )
      .then((res) => res.data);
  },

  /**
   * 提交企业认证
   * @param {object} params - 企业认证参数
   * @returns {Promise<ApiResponse>} 提交结果
   */
  submitEnterpriseAuth(params: any): Promise<ApiResponse> {
    return httpNew
      .post(
        "/hgy/personalCenter/hgyEnterpriseAuth/addOrUpdateEnterpriseAuth",
        params
      )
      .then((res) => res.data);
  },

  /**
   * 获取个人认证信息
   * @param {string} userId - 用户ID
   * @returns {Promise<ApiResponse>} 个人认证信息
   */
  getPersonalAuth(userId: string): Promise<ApiResponse> {
    return httpNew
      .get("/hgy/personalCenter/hgyPersonalAuth/getPersonalAuthByUserId", {
        params: { userId },
      })
      .then((res) => res.data);
  },

  /**
   * 获取企业认证信息
   * @param {string} userId - 用户ID
   * @returns {Promise<ApiResponse>} 企业认证信息
   */
  getEnterpriseAuth(userId: string): Promise<ApiResponse> {
    return httpNew
      .get("/hgy/personalCenter/hgyEnterpriseAuth/getEnterpriseAuthByUserId", {
        params: { userId },
      })
      .then((res) => res.data);
  },

  /**
   * 获取收藏列表
   * @param {object} params - 查询参数
   * @returns {Promise<ApiResponse>} 收藏列表
   */
  getCollectList(params: {
    pageNo?: number;
    pageSize?: number;
    userId?: string;
  }): Promise<ApiResponse> {
    return httpNew
      .get("/hgy/collect/hgyCollectLog/list", { params })
      .then((res) => res.data);
  },

  /**
   * 获取浏览记录列表
   * @param {object} params - 查询参数
   * @returns {Promise<ApiResponse>} 浏览记录列表
   */
  getBrowseList(params: {
    pageNo?: number;
    pageSize?: number;
    userId?: string;
  }): Promise<ApiResponse> {
    return httpNew
      .get("/hgy/browse/hgyBrowseLog/list", { params })
      .then((res) => res.data);
  },

  /**
   * 清除浏览记录
   * @param {string} id - 浏览记录ID
   * @returns {Promise<ApiResponse>} 清除结果
   */
  clearBrowse(id: string): Promise<ApiResponse> {
    return httpNew
      .delete(`/hgy/personalCenter/browse/delete?ids=${id}`)
      .then((res) => res.data);
  },
};

// 行业资讯信息类型定义
export interface IndustryInfo {
  id?: string;
  materialType?: string; // 物资种类
  materialType_dictText?: string; // 物资种类显示文本
  category?: string; // 品类/细类
  province?: string; // 省份
  city?: string; // 城市
  district?: string; // 区县
  highPrice?: number; // 高价
  lowPrice?: number; // 低价
  avgPrice?: number; // 均价
  changeAmount?: number; // 涨跌额
  changeRate?: number; // 涨跌幅
  infoDate?: string; // 资讯日期
  createTime?: string; // 创建时间
  updateTime?: string; // 更新时间
  createBy?: string; // 创建人
  updateBy?: string; // 更新人
}

// 行业资讯相关API
export const industryInfoApi = {
  /**
   * 获取行业资讯列表
   */
  getIndustryInfoList: (params: any): Promise<ApiResponse> => {
    return httpNew
      .get("/hgy/waste/hgyIndustryInfo/list", { params })
      .then((res) => res.data);
  },

  /**
   * 删除行业资讯
   */
  deleteIndustryInfo: (params: { id: string }): Promise<ApiResponse> => {
    return httpNew
      .delete("/hgy/waste/hgyIndustryInfo/delete", { params })
      .then((res) => res.data);
  },

  /**
   * 批量删除行业资讯
   */
  batchDeleteIndustryInfo: (params: { ids: string }): Promise<ApiResponse> => {
    return httpNew
      .delete("/hgy/waste/hgyIndustryInfo/deleteBatch", { params })
      .then((res) => res.data);
  },

  /**
   * 导出行业资讯
   */
  exportIndustryInfo: (params: any): Promise<Blob> => {
    return httpNew
      .get("/hgy/waste/hgyIndustryInfo/exportXls", {
        params,
        responseType: "blob",
      })
      .then((res) => res.data);
  },

  /**
   * 导入行业资讯
   */
  importIndustryInfo: (data: FormData): Promise<ApiResponse> => {
    return httpNew
      .post("/hgy/waste/hgyIndustryInfo/importExcel", data, {
        headers: { "Content-Type": "multipart/form-data" },
      })
      .then((res) => res.data);
  },
};

// 导出类型定义
export type {
  ApiResponse,
  BaseParams,
  MenuItem,
  MenuMeta,
  UserPermissionResponse,
};

// 默认导出
export default newApi;
