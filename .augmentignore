# Augment 索引忽略配置文件
# 此文件告诉 Augment 在建立代码索引时忽略哪些文件和目录

# 打包输出目录
jeecg_ui/
jeecg_ui/**
dist/
dist/**
参考文件/
参考文件/**

# 依赖目录
node_modules/
node_modules/**

# 缓存目录
.cache/
.cache/**
.vite/
.vite/**

# 临时文件
*.tmp
*.temp
.DS_Store
Thumbs.db

# 日志文件
*.log
logs/
logs/**

# 编辑器配置
.vscode/
.idea/
*.swp
*.swo

# 系统文件
.git/
.git/**

# 其他构建产物
coverage/
coverage/**
.nyc_output/
.nyc_output/**

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 图片和媒体文件（可选，根据需要调整）
# *.jpg
# *.jpeg
# *.png
# *.gif
# *.svg
# *.mp4
# *.mp3
# *.wav

# 文档文件（可选，根据需要调整）
# *.pdf
# *.doc
# *.docx
